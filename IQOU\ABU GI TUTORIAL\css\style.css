/* CSS Variables - Color Palette */
:root {
    --dark-moss-green: #606c38ff;
    --pakistan-green: #283618ff;
    --cornsilk: #fefae0ff;
    --earth-yellow: #dda15eff;
    --tigers-eye: #bc6c25ff;

    --primary-font: 'La<PERSON>', sans-serif;
    --heading-font: 'Merriweather', serif;

    --text-light: var(--cornsilk);
    --text-dark: var(--pakistan-green);
    --bg-light: var(--cornsilk);
    --bg-dark: var(--pakistan-green);
    
    --accent1: var(--dark-moss-green);
    --accent2: var(--earth-yellow);
    --accent3: var(--tigers-eye);

    --container-width: 1140px;
    --spacing-unit: 1rem; /* Approx 16px */
}

/* Global Reset & Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 100%; /* Allows user to zoom text */
}

body {
    font-family: var(--primary-font);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-light);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    color: var(--pakistan-green);
    margin-bottom: calc(var(--spacing-unit) * 0.75);
    line-height: 1.3;
}

h1 { font-size: 2.8rem; }
h2 { font-size: 2.2rem; margin-top: calc(var(--spacing-unit) * 2); }
h3 { font-size: 1.8rem; }
p { margin-bottom: var(--spacing-unit); }
a { color: var(--dark-moss-green); text-decoration: none; transition: color 0.3s ease; }
a:hover, a:focus { color: var(--tigers-eye); text-decoration: underline; }

/* Layout */
.container {
    width: 90%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--spacing-unit);
}

/* Header & Navigation */
header {
    background-color: var(--bg-light);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: var(--spacing-unit) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 3px solid var(--earth-yellow);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-family: var(--heading-font);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--pakistan-green);
}
.logo:hover {
    color: var(--dark-moss-green);
    text-decoration: none;
}

.nav-menu {
    list-style: none;
    display: flex;
}
.nav-menu li {
    margin-left: calc(var(--spacing-unit) * 1.5);
}
.nav-menu a {
    font-weight: 700;
    padding: 0.5rem 0;
    position: relative;
}
.nav-menu a.active, .nav-menu a:hover {
    color: var(--tigers-eye);
}
.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--tigers-eye);
    transition: width 0.3s ease;
}
.nav-menu a.active::after, .nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none; /* Hidden by default, shown on mobile */
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}
.hamburger {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--pakistan-green);
    position: relative;
    transition: transform 0.3s ease;
}
.hamburger::before, .hamburger::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--pakistan-green);
    transition: transform 0.3s ease, top 0.3s ease, bottom 0.3s ease;
}
.hamburger::before { top: -8px; }
.hamburger::after { bottom: -8px; }

/* Active state for hamburger */
.nav-open .hamburger { transform: rotate(45deg); }
.nav-open .hamburger::before { top: 0; transform: rotate(90deg); }
.nav-open .hamburger::after { bottom: 0; display: none; }


/* Hero Section */
.hero {
    position: relative;
    color: var(--text-light);
    text-align: center;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--spacing-unit) * 4) 0;
}
.hero-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/hero_bg_placeholder.jpg') no-repeat center center/cover; /* Replace with actual image */
    filter: brightness(0.5); /* Darken the image for text readability */
    z-index: -1;
}
.hero-content h1 {
    font-size: 3.2rem;
    color: var(--text-light); /* White or light color for hero text */
    margin-bottom: var(--spacing-unit);
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}
.hero-content p {
    font-size: 1.3rem;
    margin-bottom: calc(var(--spacing-unit) * 2);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: calc(var(--spacing-unit)*0.75) calc(var(--spacing-unit)*1.5);
    border-radius: 5px;
    font-weight: 700;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    margin: calc(var(--spacing-unit)*0.5);
}
.btn-primary {
    background-color: var(--tigers-eye);
    color: var(--text-light);
    border-color: var(--tigers-eye);
}
.btn-primary:hover {
    background-color: #a0581e; /* Darker tigers-eye */
    border-color: #a0581e;
    color: var(--text-light);
    text-decoration: none;
}
.btn-secondary {
    background-color: var(--dark-moss-green);
    color: var(--text-light);
    border-color: var(--dark-moss-green);
}
.btn-secondary:hover {
    background-color: #4a552b; /* Darker moss green */
    border-color: #4a552b;
    color: var(--text-light);
    text-decoration: none;
}
.btn-outline {
    background-color: transparent;
    color: var(--dark-moss-green);
    border-color: var(--dark-moss-green);
}
.btn-outline:hover {
    background-color: var(--dark-moss-green);
    color: var(--text-light);
    text-decoration: none;
}
.btn-primary-outline { /* New style */
    background-color: transparent;
    color: var(--tigers-eye);
    border-color: var(--tigers-eye);
}
.btn-primary-outline:hover {
    background-color: var(--tigers-eye);
    color: var(--text-light);
    text-decoration: none;
}
.btn-small {
    padding: calc(var(--spacing-unit)*0.5) var(--spacing-unit);
    font-size: 0.9rem;
}


/* Intro Section */
.intro-section { padding: calc(var(--spacing-unit) * 3) 0; }
.intro-content {
    display: flex;
    gap: calc(var(--spacing-unit) * 2);
    align-items: center;
}
.intro-text { flex: 2; }
.intro-image { flex: 1; text-align: center; }
.intro-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Featured Content Section */
.featured-content { padding: calc(var(--spacing-unit) * 3) 0; }
.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: calc(var(--spacing-unit) * 2);
}
.featured-item {
    background-color: #fff; /* Slightly off-white if needed */
    padding: calc(var(--spacing-unit) * 1.5);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    text-align: center;
    border-left: 5px solid var(--earth-yellow);
}
.featured-item img {
    max-width: 80px; /* Or adjust as needed */
    margin-bottom: var(--spacing-unit);
}
.featured-item h3 { color: var(--pakistan-green); margin-top: 0; }
.learn-more {
    display: inline-block;
    margin-top: var(--spacing-unit);
    font-weight: 700;
    color: var(--dark-moss-green);
}
.learn-more i { margin-left: 5px; transition: transform 0.3s ease; }
.learn-more:hover i { transform: translateX(5px); }


/* Vision Snippet */
.vision-snippet {
    background-color: var(--pakistan-green);
    color: var(--cornsilk);
    padding: calc(var(--spacing-unit) * 3) 0;
    text-align: center;
}
.vision-snippet h3 { color: var(--cornsilk); }
.vision-snippet p {
    font-size: 1.2rem;
    font-style: italic;
    max-width: 800px;
    margin: 0 auto;
}

/* Featured Video Section */
.featured-video-section {
    padding: calc(var(--spacing-unit) * 3) 0;
    text-align: center;
}
.featured-video-section .video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    max-width: 800px; /* Adjust as needed */
    margin: 0 auto var(--spacing-unit);
    border: 5px solid var(--earth-yellow);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.featured-video-section .video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.video-caption {
    font-size: 0.9rem;
    color: var(--dark-moss-green);
    margin-top: var(--spacing-unit);
}


/* Page Header (for subpages) */
.page-header {
    background-color: var(--dark-moss-green);
    color: var(--cornsilk);
    padding: calc(var(--spacing-unit) * 3) 0;
    text-align: center;
}
.page-header h1 { color: var(--cornsilk); margin-bottom: calc(var(--spacing-unit)*0.5); }
.page-header p { font-size: 1.1rem; color: var(--cornsilk); opacity: 0.9; margin-bottom: 0; }

/* About Page Specifics */
.about-detailed { padding: calc(var(--spacing-unit) * 3) 0; }
.content-block { margin-bottom: calc(var(--spacing-unit) * 2.5); }
.content-block h2 { border-bottom: 2px solid var(--earth-yellow); padding-bottom: 0.5rem; display: inline-block; }
.profile-image-large {
    float: right;
    width: 350px; /* Adjust as needed */
    margin-left: calc(var(--spacing-unit) * 2);
    margin-bottom: var(--spacing-unit);
    border-radius: 8px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}
.content-block ul {
    list-style: none; /* Or disc/circle */
    padding-left: 0; /* If no list-style, else 20px */
}
.content-block ul li {
    padding-left: 1.5em;
    position: relative;
    margin-bottom: 0.5em;
}
.content-block ul li::before {
    content: "\f005"; /* FontAwesome star icon */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: var(--tigers-eye);
    position: absolute;
    left: 0;
    top: 2px;
}

/* Teachings & Resources Page */
.resources-section { padding: calc(var(--spacing-unit) * 3) 0; }
.search-filter-bar {
    display: flex;
    gap: var(--spacing-unit);
    margin-bottom: calc(var(--spacing-unit) * 2);
    align-items: center;
}
.search-filter-bar input[type="search"], .search-filter-bar select {
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-family: var(--primary-font);
    flex-grow: 1;
}
.search-filter-bar select { flex-grow: 0; min-width: 200px; }
.search-filter-bar button { padding: 0.75rem 1.5rem; }

.resource-category {
    margin-bottom: calc(var(--spacing-unit) * 3);
    padding: calc(var(--spacing-unit) * 2);
    border-radius: 8px;
}
.resource-category h2 { margin-top: 0; }
.icon-color { color: var(--tigers-eye); margin-right: 0.5em; }

.resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: calc(var(--spacing-unit) * 1.5);
}
.card { /* Generic card style */
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.07);
    padding: calc(var(--spacing-unit) * 1.5);
    display: flex;
    flex-direction: column;
    border-left: 4px solid var(--dark-moss-green);
}
.resource-item h3 { margin-top: 0; font-size: 1.4rem; color: var(--pakistan-green); }
.resource-item p { flex-grow: 1; }
.book-showcase {
    flex-direction: row;
    align-items: flex-start;
    gap: var(--spacing-unit);
}
.book-showcase img {
    max-width: 100px; /* Adjust as needed */
    height: auto;
    border: 1px solid #eee;
}
.video-item .video-container-embed {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    overflow: hidden;
    width: 100%;
    margin-bottom: var(--spacing-unit);
}
.video-item .video-container-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.video-item h3 { font-size: 1.2rem; }

/* News & Insights Page */
.news-updates-section { padding: calc(var(--spacing-unit) * 3) 0; }
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: calc(var(--spacing-unit) * 2);
    margin-bottom: calc(var(--spacing-unit) * 2);
}
.news-item-image {
    width: 100%;
    height: 200px; /* Fixed height for consistency */
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}
.news-item-content { padding: var(--spacing-unit); }
.news-item-date {
    font-size: 0.85rem;
    color: var(--dark-moss-green);
    display: block;
    margin-bottom: calc(var(--spacing-unit) * 0.5);
}
.news-item-title { font-size: 1.5rem; margin-bottom: calc(var(--spacing-unit) * 0.5); }
.news-item-excerpt { margin-bottom: var(--spacing-unit); }
.pagination {
    text-align: center;
    margin-top: calc(var(--spacing-unit) * 2);
}
.page-item {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border: 1px solid var(--earth-yellow);
    color: var(--earth-yellow);
    border-radius: 4px;
}
.page-item.active, .page-item:hover {
    background-color: var(--earth-yellow);
    color: var(--text-light);
    text-decoration: none;
}
.social-media-feed {
    margin-top: calc(var(--spacing-unit) * 3);
    padding: calc(var(--spacing-unit) * 2);
    text-align: center;
    border-radius: 8px;
}
.social-links-large a {
    display: inline-block;
    margin: 0.5rem 1rem;
    font-size: 1.1rem;
    color: var(--pakistan-green);
}
.social-links-large a:hover { color: var(--tigers-eye); }
.social-links-large i { margin-right: 0.5rem; }

/* Events Page */
.events-section { padding: calc(var(--spacing-unit) * 3) 0; }
.calendar-container { /* Placeholder for actual calendar */
    min-height: 400px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    margin-bottom: calc(var(--spacing-unit) * 2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.events-list { display: flex; flex-direction: column; gap: calc(var(--spacing-unit) * 1.5); }
.event-item {
    display: flex;
    gap: calc(var(--spacing-unit) * 1.5);
    align-items: flex-start;
}
.event-date-badge {
    background-color: var(--pakistan-green);
    color: var(--cornsilk);
    padding: var(--spacing-unit);
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
}
.event-date-badge .month { font-size: 0.9rem; text-transform: uppercase; display: block; }
.event-date-badge .day { font-size: 1.8rem; font-weight: 700; display: block; line-height: 1; }
.event-date-badge .year { font-size: 0.9rem; display: block; }

.event-details h3 { margin-top: 0; font-size: 1.5rem; }
.event-details p { margin-bottom: 0.5rem; }
.event-details i { color: var(--dark-moss-green); margin-right: 0.5rem; }
.event-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 700;
    margin-top: 0.5rem;
}
.event-type.online { background-color: var(--earth-yellow); color: var(--pakistan-green); }
.event-type.offline { background-color: var(--dark-moss-green); color: var(--cornsilk); }
.past-events-title { margin-top: calc(var(--spacing-unit) * 3); }
.past-event { opacity: 0.7; }
.past-event .event-date-badge { background-color: #aaa; }

/* Gallery Page */
.gallery-section { padding: calc(var(--spacing-unit) * 3) 0; }
.gallery-album-title { margin-top: calc(var(--spacing-unit) * 2.5); }
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-unit);
}
.gallery-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.07);
}
.gallery-item img {
    width: 100%;
    height: 200px; /* Fixed height */
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}
.gallery-item a:hover img { transform: scale(1.05); }
.gallery-caption {
    padding: calc(var(--spacing-unit) * 0.75);
    font-size: 0.9rem;
    text-align: center;
    color: var(--dark-moss-green);
}
/* Lightbox styles (basic, rely on lightbox library for full styling) */
.lightboxOverlay { background-color: rgba(0,0,0,0.8) !important; }

/* Contact Page */
.contact-section { padding: calc(var(--spacing-unit) * 3) 0; }
.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Default for larger screens */
    gap: calc(var(--spacing-unit) * 3);
}
.contact-form-container, .contact-info-container {
    padding: calc(var(--spacing-unit) * 1.5);
}
.contact-form-container h2, .contact-info-container h2 { margin-top: 0; }
.form-group { margin-bottom: var(--spacing-unit); }
.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 700;
    color: var(--pakistan-green);
}
.form-group .required { color: var(--tigers-eye); }
.form-group input[type="text"],
.form-group input[type="email"],
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-family: var(--primary-font);
    font-size: 1rem;
}
.form-group textarea { resize: vertical; }
#form-status { margin-top: var(--spacing-unit); font-weight: bold; }

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: calc(var(--spacing-unit) * 1.5);
}
.icon-contact {
    font-size: 1.5rem;
    color: var(--dark-moss-green);
    margin-right: var(--spacing-unit);
    margin-top: 0.25rem;
    width: 30px; /* Fixed width for alignment */
    text-align: center;
}
.contact-info-item strong { display: block; color: var(--pakistan-green); }
.contact-info-item p { margin-bottom: 0.25rem; }
.social-links-contact a {
    font-size: 1.8rem;
    margin-right: var(--spacing-unit);
    color: var(--pakistan-green);
}
.social-links-contact a:hover { color: var(--tigers-eye); }


/* Footer */
footer {
    background-color: var(--pakistan-green);
    color: var(--cornsilk);
    padding: calc(var(--spacing-unit) * 3) 0 calc(var(--spacing-unit) * 1) 0;
    margin-top: calc(var(--spacing-unit) * 3);
    border-top: 5px solid var(--earth-yellow);
}
.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: calc(var(--spacing-unit) * 2);
    margin-bottom: calc(var(--spacing-unit) * 2);
}
.footer-col h4 {
    color: var(--earth-yellow);
    margin-bottom: var(--spacing-unit);
    font-size: 1.3rem;
}
.footer-col p, .footer-col ul li {
    font-size: 0.95rem;
    opacity: 0.9;
}
.footer-col ul { list-style: none; }
.footer-col ul li a { color: var(--cornsilk); }
.footer-col ul li a:hover { color: var(--earth-yellow); text-decoration: underline; }
.social-links a {
    color: var(--cornsilk);
    font-size: 1.5rem;
    margin-right: var(--spacing-unit);
    transition: color 0.3s ease;
}
.social-links a:hover { color: var(--earth-yellow); }
.footer-bottom {
    text-align: center;
    padding-top: calc(var(--spacing-unit) * 1.5);
    border-top: 1px solid var(--dark-moss-green);
    font-size: 0.9rem;
}
.footer-bottom p { margin-bottom: 0.5rem; opacity: 0.8; }
.footer-bottom a { color: var(--cornsilk); opacity: 0.8; }
.footer-bottom a:hover { color: var(--earth-yellow); opacity: 1; }


/* Subtle Islamic Geometric Pattern Background */
.patterned-bg {
    background-color: var(--cornsilk); /* Fallback */
    background-image: 
        linear-gradient(135deg, var(--dark-moss-green) 10%, transparent 10%),
        linear-gradient(225deg, var(--dark-moss-green) 10%, transparent 10%),
        linear-gradient(45deg, var(--dark-moss-green) 10%, transparent 10%),
        linear-gradient(315deg, var(--dark-moss-green) 10%, var(--cornsilk) 10%);
    background-size: 20px 20px; /* Adjust size of pattern */
    background-position: 0 0, 10px 0, 0 10px, 10px 10px; /* Adjust position for effect */
    animation: pattern-scroll 60s linear infinite; /* Subtle scroll */
}
.patterned-bg-light { /* More subtle version for content areas */
    background-color: var(--cornsilk);
    background-image: 
        radial-gradient(var(--earth-yellow) 1px, transparent 1px); /* Subtle dots */
    background-size: 15px 15px; /* Adjust size of pattern */
    padding-top: calc(var(--spacing-unit) * 2);
    padding-bottom: calc(var(--spacing-unit) * 2);
    border-top: 1px solid var(--earth-yellow_05); /* Using a hypothetical lighter earth-yellow */
    border-bottom: 1px solid var(--earth-yellow_05);
}

@keyframes pattern-scroll {
    0% { background-position: 0 0, 10px 0, 0 10px, 10px 10px; }
    100% { background-position: 20px 20px, 30px 20px, 20px 30px, 30px 30px; }
}


/* Accessibility */
a:focus, button:focus, input:focus, textarea:focus, select:focus {
    outline: 2px solid var(--tigers-eye);
    outline-offset: 2px;
}
.sr-only { /* Screen reader only */
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    border: 0;
}


/* Responsive Design */
@media (max-width: 992px) {
    .intro-content { flex-direction: column; text-align: center; }
    .intro-image img { max-width: 70%; }
    .profile-image-large { float: none; display: block; margin: 0 auto calc(var(--spacing-unit) * 2); max-width: 280px; }
    .contact-grid { grid-template-columns: 1fr; }
}

@media (max-width: 768px) {
    .nav-toggle { display: block; }
    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%; /* Position below header */
        left: 0;
        width: 100%;
        background-color: var(--bg-light);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding-bottom: var(--spacing-unit);
    }
    .nav-menu.nav-open { display: flex; }
    .nav-menu li {
        margin-left: 0;
        text-align: center;
    }
    .nav-menu a {
        display: block;
        padding: var(--spacing-unit) 0;
        border-bottom: 1px solid #eee;
    }
    .nav-menu a::after { display: none; } /* Remove underline animation for mobile menu */
    .nav-menu li:last-child a { border-bottom: none; }

    h1 { font-size: 2.4rem; }
    .hero-content h1 { font-size: 2.5rem; }
    .hero-content p { font-size: 1.1rem; }
    .featured-grid, .news-grid, .gallery-grid { grid-template-columns: 1fr; } /* Stack items */
    .search-filter-bar { flex-direction: column; }
    .search-filter-bar select, .search-filter-bar button { width: 100%; }
    .book-showcase { flex-direction: column; align-items: center; text-align: center; }
    .book-showcase img { margin-bottom: var(--spacing-unit); }
    .event-item { flex-direction: column; align-items: center; text-align: center; }
    .event-date-badge { margin-bottom: var(--spacing-unit); }
}