document.addEventListener('DOMContentLoaded', function() {

    // Mobile Navigation Toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('nav-open');
            navToggle.classList.toggle('nav-open'); // For hamburger animation
            // ARIA attribute for accessibility
            const isExpanded = navMenu.classList.contains('nav-open');
            navToggle.setAttribute('aria-expanded', isExpanded);
        });
    }

    // Smooth Scrolling for Anchor Links (if any specific ones are needed beyond default browser behavior)
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');
            // Check if it's a real anchor and not just '#'
            if (href.length > 1 && document.querySelector(href)) {
                e.preventDefault();
                document.querySelector(href).scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Update Footer Year
    const currentYearSpan = document.getElementById('currentYear');
    if (currentYearSpan) {
        currentYearSpan.textContent = new Date().getFullYear();
    }

    // Contact Form Validation (Basic Example)
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent actual submission for this static example
            
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();
            const formStatus = document.getElementById('form-status');

            if (!name || !email || !subject || !message) {
                formStatus.textContent = 'Please fill in all required fields.';
                formStatus.style.color = 'var(--tigers-eye)';
                return;
            }

            if (!validateEmail(email)) {
                formStatus.textContent = 'Please enter a valid email address.';
                formStatus.style.color = 'var(--tigers-eye)';
                return;
            }

            // If validation passes (for a real form, you'd submit data here)
            formStatus.textContent = 'Thank you for your message! (This is a demo)';
            formStatus.style.color = 'var(--dark-moss-green)';
            contactForm.reset(); 
        });
    }

    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(String(email).toLowerCase());
    }

    // Lightbox Initialization (if Lightbox2 is used and linked)
    if (typeof lightbox !== 'undefined') {
        lightbox.option({
          'resizeDuration': 200,
          'wrapAround': true,
          'disableScrolling': true
        });
    }

    // Active navigation link highlighting based on current page
    const currentPage = window.location.pathname.split("/").pop() || "index.html";
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        } else {
            link.classList.remove('active'); // Ensure only current page is active
        }
    });
    // Special case for index.html if URL is just '/'
    if (currentPage === "" || currentPage === "index.html") {
        const homeLink = document.querySelector('.nav-menu a[href="index.html"]');
        if (homeLink) homeLink.classList.add('active');
    }


    // Subtle Animations on Scroll (Intersection Observer API Example)
    const animatedElements = document.querySelectorAll('.featured-item, .news-item, .resource-item, .content-block, .event-item');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = `fadeInUp 0.8s ease forwards`;
                observer.unobserve(entry.target); // Optional: stop observing once animated
            }
        });
    }, { threshold: 0.1 }); // Trigger when 10% of the element is visible

    animatedElements.forEach(el => {
        el.style.opacity = '0'; // Start transparent
        observer.observe(el);
    });

    // Add CSS keyframes for fadeInUp animation (can also be in style.css)
    const styleSheet = document.styleSheets[0];
    styleSheet.insertRule(`
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }`, styleSheet.cssRules.length);

});